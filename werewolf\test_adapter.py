#!/usr/bin/env python3
"""
测试新的LLM适配器系统
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_adapter_import():
    """测试适配器导入"""
    try:
        from llm_adapter import BasicRoleAgentAdapter, LLMAdapter
        print("✅ 适配器导入成功")
        return True
    except Exception as e:
        print(f"❌ 适配器导入失败: {e}")
        return False

def test_agent_import():
    """测试Agent导入"""
    try:
        from villager.villager_agent import VillagerAgent
        from witch.witch_agent import WitchAgent
        from seer.seer_agent import SeerAgent
        from wolf.wolf_agent import WolfAgent
        print("✅ 所有Agent导入成功")
        return True
    except Exception as e:
        print(f"❌ Agent导入失败: {e}")
        return False

def test_agent_creation():
    """测试Agent创建"""
    try:
        from villager.villager_agent import VillagerAgent
        
        # 创建一个测试Agent
        agent = VillagerAgent(model_name="test-model")
        print("✅ Agent创建成功")
        
        # 测试memory功能
        agent.memory.set_variable("test", "value")
        result = agent.memory.load_variable("test")
        if result == "value":
            print("✅ Memory功能正常")
        else:
            print("❌ Memory功能异常")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Agent创建失败: {e}")
        return False

def test_environment_config():
    """测试环境配置"""
    print("\n=== 环境配置检查 ===")
    
    required_vars = ['MODEL_SERIES', 'MODEL_NAME', 'API_KEY', 'BASE_URL']
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            if var == 'API_KEY':
                print(f"{var}: {'*' * len(value)}")  # 隐藏API密钥
            else:
                print(f"{var}: {value}")
        else:
            missing_vars.append(var)
            print(f"{var}: 未设置")
    
    if missing_vars:
        print(f"\n❌ 缺少环境变量: {', '.join(missing_vars)}")
        print("请在.env文件中设置这些变量")
        return False
    else:
        print("\n✅ 所有必需的环境变量都已设置")
        return True

def main():
    """主测试函数"""
    print("狼人杀游戏 - LLM适配器系统测试")
    print("=" * 50)
    
    # 加载环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ 环境变量加载成功")
    except ImportError:
        print("⚠️  python-dotenv未安装，跳过.env文件加载")
    except Exception as e:
        print(f"⚠️  环境变量加载失败: {e}")
    
    tests = [
        ("适配器导入测试", test_adapter_import),
        ("Agent导入测试", test_agent_import),
        ("Agent创建测试", test_agent_creation),
        ("环境配置测试", test_environment_config),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        else:
            print(f"测试失败: {test_name}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！系统已准备就绪。")
        return True
    else:
        print("❌ 部分测试失败，请检查配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
