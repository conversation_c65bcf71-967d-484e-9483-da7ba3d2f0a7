#!/usr/bin/env python3
"""
测试策略修复
验证单预言家投票策略和死亡机制知识的修复
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_single_seer_strategy_fix():
    """测试单预言家投票策略修复"""
    print("=== 测试单预言家投票策略修复 ===")
    
    roles = [
        ("平民", "villager.prompt"),
        ("女巫", "witch.prompt")
    ]
    
    all_passed = True
    
    for role_name, module_path in roles:
        print(f"\n--- 检查 {role_name} 单预言家策略 ---")
        
        try:
            if module_path == "villager.prompt":
                from villager.prompt import ACTION_TEMPLATES
            elif module_path == "witch.prompt":
                from witch.prompt import ACTION_TEMPLATES
            
            # 检查投票模板
            if "vote" in ACTION_TEMPLATES:
                vote_template = ACTION_TEMPLATES["vote"]
                
                # 检查是否移除了盲信策略
                bad_patterns = [
                    "优先相信预言家的查杀结果",
                    "优先相信预言家的判断",
                    "无脑相信预言家"
                ]
                
                # 检查是否包含正确的策略
                good_patterns = [
                    "要仔细分析",
                    "不能盲信",
                    "可能是悍跳狼"
                ]
                
                bad_found = []
                good_found = []
                
                for pattern in bad_patterns:
                    if pattern in vote_template:
                        bad_found.append(pattern)
                
                for pattern in good_patterns:
                    if pattern in vote_template:
                        good_found.append(pattern)
                
                if bad_found:
                    print(f"  ❌ 仍包含错误策略: {bad_found}")
                    all_passed = False
                else:
                    print(f"  ✅ 已移除错误的盲信策略")
                
                if good_found:
                    print(f"  ✅ 包含正确策略: {good_found}")
                else:
                    print(f"  ⚠️  缺少正确的分析策略")
                    all_passed = False
            else:
                print(f"  ⚠️  没有找到投票模板")
                all_passed = False
                
        except Exception as e:
            print(f"  ❌ {role_name} 测试失败: {e}")
            all_passed = False
    
    return all_passed

def test_death_mechanism_knowledge():
    """测试死亡机制知识修复"""
    print("\n=== 测试死亡机制知识修复 ===")
    
    roles = [
        ("平民", "villager.prompt"),
        ("预言家", "seer.prompt"),
        ("女巫", "witch.prompt"),
        ("狼人", "wolf.prompt")
    ]
    
    all_passed = True
    
    for role_name, module_path in roles:
        print(f"\n--- 检查 {role_name} 死亡机制知识 ---")
        
        try:
            if module_path == "villager.prompt":
                from villager.prompt import GAME_RULES_SUMMARY
            elif module_path == "seer.prompt":
                from seer.prompt import GAME_RULES_SUMMARY
            elif module_path == "witch.prompt":
                from witch.prompt import GAME_RULES_SUMMARY
            elif module_path == "wolf.prompt":
                from wolf.prompt import GAME_RULES_SUMMARY
            
            # 检查死亡机制知识
            required_knowledge = [
                "狼人每晚必定出刀",
                "不存在空刀情况",
                "如果一晚死了两个人，说明狼刀了一个，女巫毒了一个",
                "狼刀和女巫毒重合在同一目标",
                "女巫用解药救了被狼刀的人"
            ]
            
            # 检查错误知识是否已移除
            wrong_knowledge = [
                "狼人空刀",
                "或者狼人空刀了"
            ]
            
            knowledge_found = []
            wrong_found = []
            
            for knowledge in required_knowledge:
                if knowledge in GAME_RULES_SUMMARY:
                    knowledge_found.append(knowledge)
            
            for wrong in wrong_knowledge:
                if wrong in GAME_RULES_SUMMARY:
                    wrong_found.append(wrong)
            
            if wrong_found:
                print(f"  ❌ 仍包含错误知识: {wrong_found}")
                all_passed = False
            else:
                print(f"  ✅ 已移除错误的空刀概念")
            
            if len(knowledge_found) >= 3:  # 至少包含3个关键知识点
                print(f"  ✅ 包含正确死亡机制知识: {len(knowledge_found)}/5")
            else:
                print(f"  ⚠️  死亡机制知识不完整: {len(knowledge_found)}/5")
                all_passed = False
                
        except Exception as e:
            print(f"  ❌ {role_name} 测试失败: {e}")
            all_passed = False
    
    return all_passed

def test_prompt_generation_with_fixes():
    """测试修复后的提示词生成"""
    print("\n=== 测试修复后的提示词生成 ===")
    
    try:
        from villager.prompt import build_prompt as villager_build_prompt
        from witch.prompt import build_prompt as witch_build_prompt
        
        # 测试平民投票提示词
        villager_vote_prompt = villager_build_prompt(
            "vote",
            "rational",
            name="测试平民",
            history="薄伽丘：6号发言：我是预言家，验了2号是狼人",
            choices=["2号", "3号", "4号", "5号", "6号"]
        )
        
        # 检查是否包含正确的策略
        villager_checks = [
            "要仔细分析" in villager_vote_prompt,
            "不能盲信" in villager_vote_prompt,
            "狼人每晚必定出刀" in villager_vote_prompt,
            "不存在空刀" in villager_vote_prompt
        ]
        
        if all(villager_checks):
            print("✅ 平民投票提示词修复正确")
        else:
            print("❌ 平民投票提示词仍有问题")
            return False
        
        # 测试女巫投票提示词
        witch_vote_prompt = witch_build_prompt(
            "vote",
            "strategic",
            name="测试女巫",
            history="薄伽丘：6号发言：我是预言家，验了2号是狼人",
            choices=["2号", "3号", "4号", "5号", "6号"]
        )
        
        # 检查是否包含正确的策略
        witch_checks = [
            "要仔细分析" in witch_vote_prompt,
            "可能是悍跳狼" in witch_vote_prompt,
            "狼人每晚必定出刀" in witch_vote_prompt,
            "不存在空刀" in witch_vote_prompt
        ]
        
        if all(witch_checks):
            print("✅ 女巫投票提示词修复正确")
        else:
            print("❌ 女巫投票提示词仍有问题")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 提示词生成测试失败: {e}")
        return False

def show_fix_summary():
    """展示修复总结"""
    print("\n=== 修复总结 ===")
    
    print("📝 问题1修复 - 单预言家投票策略:")
    print("  ❌ 修复前: '如果只有一个预言家跳出来，优先相信预言家的查杀结果'")
    print("  ✅ 修复后: '如果只有一个预言家跳出来，要仔细分析他的验人逻辑和被验者反应，不能盲信'")
    print("  💡 原因: 单个预言家可能是悍跳狼，不能无脑相信")
    
    print("\n📝 问题2修复 - 死亡机制知识:")
    print("  ❌ 修复前: '如果昨晚是平安夜，说明女巫可能救了人，或者狼人空刀了'")
    print("  ✅ 修复后: '狼人每晚必定出刀击杀一名玩家，不存在空刀情况'")
    print("  💡 新增知识:")
    print("    - 如果一晚死了两个人，说明狼刀了一个，女巫毒了一个")
    print("    - 如果一晚只死了一个人，可能是：①只有狼刀 ②狼刀和女巫毒重合")
    print("    - 如果一晚没有人死亡（平安夜），说明女巫用解药救了被狼刀的人")

def main():
    """主测试函数"""
    print("狼人杀游戏 - 策略修复验证")
    print("=" * 50)
    
    tests = [
        ("单预言家策略修复", test_single_seer_strategy_fix),
        ("死亡机制知识修复", test_death_mechanism_knowledge),
        ("修复后提示词生成", test_prompt_generation_with_fixes)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        else:
            print(f"测试失败: {test_name}")
    
    # 显示修复总结
    show_fix_summary()
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有策略修复测试通过！")
        print("\n现在AI应该:")
        print("1. ✅ 不会无脑相信单个预言家")
        print("2. ✅ 正确理解死亡机制（狼人必定出刀）")
        print("3. ✅ 能正确分析平安夜和多死情况")
        return True
    else:
        print("❌ 部分测试失败，请检查修复。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
