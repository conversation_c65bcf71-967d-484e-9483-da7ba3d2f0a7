#!/usr/bin/env python3
"""
Gemini API 故障排除工具
帮助诊断和解决 Gemini API 连接问题
"""

import os
import sys
import httpx

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_environment_config():
    """检查环境配置"""
    print("=== 环境配置检查 ===")
    
    required_vars = ['MODEL_SERIES', 'MODEL_NAME', 'API_KEY', 'BASE_URL']
    config = {}
    
    for var in required_vars:
        value = os.getenv(var)
        config[var] = value
        if var == 'API_KEY':
            print(f"{var}: {'已设置' if value else '未设置'}")
        else:
            print(f"{var}: {value}")
    
    # 检查 Gemini 特定配置
    if config['MODEL_SERIES'] != 'gemini':
        print(f"\n⚠️  当前模型系列是 '{config['MODEL_SERIES']}'，不是 'gemini'")
        print("如果要测试 Gemini API，请设置 MODEL_SERIES=gemini")
        return False
    
    if not config['API_KEY']:
        print("\n❌ API_KEY 未设置")
        print("请在 .env 文件中设置有效的 Gemini API 密钥")
        return False
    
    if not config['BASE_URL']:
        print("\n❌ BASE_URL 未设置")
        print("Gemini API 的 BASE_URL 应该是: https://generativelanguage.googleapis.com")
        return False
    
    # 检查 BASE_URL 格式
    expected_base_url = "https://generativelanguage.googleapis.com"
    if not config['BASE_URL'].startswith(expected_base_url):
        print(f"\n⚠️  BASE_URL 可能不正确")
        print(f"当前: {config['BASE_URL']}")
        print(f"建议: {expected_base_url}")
    
    # 检查模型名称格式
    model_name = config['MODEL_NAME']
    if model_name and not model_name.startswith('gemini'):
        print(f"\n⚠️  模型名称可能不正确: {model_name}")
        print("Gemini 模型名称通常以 'gemini' 开头，如: gemini-1.5-pro, gemini-2.5-flash-preview-05-20")
    
    print("\n✅ 环境配置检查完成")
    return True

def test_api_endpoint():
    """测试 API 端点可达性"""
    print("\n=== API 端点测试 ===")
    
    base_url = os.getenv('BASE_URL')
    model_name = os.getenv('MODEL_NAME')
    api_key = os.getenv('API_KEY')
    
    if not all([base_url, model_name, api_key]):
        print("❌ 配置不完整，跳过端点测试")
        return False
    
    # 构建测试 URL
    test_url = f"{base_url}/v1beta/models/{model_name}:generateContent"
    
    print(f"测试 URL: {test_url}")
    
    # 测试请求
    headers = {"Content-Type": "application/json"}
    params = {"key": api_key}
    
    test_payload = {
        "contents": [
            {
                "role": "user",
                "parts": [{"text": "Hello"}]
            }
        ],
        "generationConfig": {
            "temperature": 0.7,
            "maxOutputTokens": 10,
            "candidateCount": 1
        }
    }
    
    try:
        with httpx.Client(timeout=30.0) as client:
            response = client.post(test_url, headers=headers, json=test_payload, params=params)
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ API 端点测试成功！")
                result = response.json()
                if "candidates" in result:
                    content = result["candidates"][0]["content"]["parts"][0]["text"]
                    print(f"响应内容: {content}")
                return True
            else:
                print(f"❌ API 端点测试失败")
                print(f"响应内容: {response.text}")
                
                # 提供具体的错误诊断
                if response.status_code == 404:
                    print("\n🔍 404 错误诊断:")
                    print("- 可能原因：模型名称不存在或 API 端点路径错误")
                    print("- 建议检查：")
                    print(f"  1. 模型名称是否正确: {model_name}")
                    print("  2. 是否使用了正确的 API 版本 (v1beta)")
                    print("  3. 模型是否在您的地区可用")
                    
                elif response.status_code == 401:
                    print("\n🔍 401 错误诊断:")
                    print("- 可能原因：API 密钥无效")
                    print("- 建议检查：")
                    print("  1. API 密钥是否正确")
                    print("  2. API 密钥是否已启用 Generative Language API")
                    print("  3. 是否在正确的 Google Cloud 项目中")
                    
                elif response.status_code == 403:
                    print("\n🔍 403 错误诊断:")
                    print("- 可能原因：权限不足或配额限制")
                    print("- 建议检查：")
                    print("  1. API 密钥是否有访问权限")
                    print("  2. 是否超出了 API 配额限制")
                    print("  3. 是否启用了计费")
                
                return False
                
    except Exception as e:
        print(f"❌ 网络请求失败: {e}")
        print("\n🔍 网络错误诊断:")
        print("- 可能原因：网络连接问题")
        print("- 建议检查：")
        print("  1. 网络连接是否正常")
        print("  2. 是否需要代理设置")
        print("  3. 防火墙是否阻止了连接")
        return False

def provide_configuration_guide():
    """提供配置指南"""
    print("\n=== Gemini API 配置指南 ===")
    
    print("\n1. 获取 API 密钥:")
    print("   - 访问 Google AI Studio: https://aistudio.google.com/")
    print("   - 登录您的 Google 账户")
    print("   - 点击 'Get API key' 创建新的 API 密钥")
    print("   - 复制生成的 API 密钥")
    
    print("\n2. 配置环境变量:")
    print("   在 .env 文件中设置以下变量:")
    print("   MODEL_SERIES=gemini")
    print("   MODEL_NAME=gemini-1.5-pro")
    print("   API_KEY=your-actual-api-key-here")
    print("   BASE_URL=https://generativelanguage.googleapis.com")
    
    print("\n3. 支持的模型:")
    print("   - gemini-1.5-pro")
    print("   - gemini-1.5-flash")
    print("   - gemini-2.5-flash-preview-05-20")
    print("   - 其他 Gemini 系列模型")
    
    print("\n4. 注意事项:")
    print("   - 确保启用了 Generative Language API")
    print("   - 某些模型可能需要加入候补名单")
    print("   - 注意 API 配额和计费限制")

def main():
    """主函数"""
    print("Gemini API 故障排除工具")
    print("=" * 50)
    
    # 加载环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ 环境变量加载成功")
    except ImportError:
        print("⚠️  python-dotenv 未安装，跳过 .env 文件加载")
    except Exception as e:
        print(f"⚠️  环境变量加载失败: {e}")
    
    # 执行检查
    config_ok = check_environment_config()
    
    if config_ok:
        endpoint_ok = test_api_endpoint()
        
        if endpoint_ok:
            print("\n🎉 Gemini API 配置正确，可以正常使用！")
            return 0
        else:
            print("\n❌ API 端点测试失败")
    else:
        print("\n❌ 环境配置有问题")
    
    # 提供配置指南
    provide_configuration_guide()
    
    return 1

if __name__ == "__main__":
    sys.exit(main())
