from mock_sdk import (
    <PERSON><PERSON><PERSON>_WITCH, <PERSON><PERSON><PERSON><PERSON>, Agent<PERSON>e<PERSON>, logger,
    STATUS_START, STATUS_WOLF_SPEECH, STATUS_VOTE_RESULT, STATUS_SKILL,
    STATUS_SKILL_RESULT, STATUS_NIGHT_INFO, STATUS_DAY, STATUS_DISCUSS,
    STATUS_VOTE, STATUS_RESULT, STATUS_NIGHT
)
from llm_adapter import BasicRoleAgentAdapter
from common.safe_format import format_prompt
from witch.prompt import (
    DESC_PROMPT, VOTE_PROMPT, SKILL_PROMPT,
    BASE_PERSONA_PROMPT, GAME_RULES_SUMMARY, WITCH_ROLE_PROMPT,
    ACTION_TEMPLATES, STRATEGY_TEMPLATES, OUTPUT_TEMPLATES,
    build_prompt
)


class WitchAgent(BasicRoleAgentAdapter):
    """女巫角色Agent"""

    def __init__(self, model_name):
        super().__init__(role=ROLE_WITCH, model_name=model_name)
        # 初始化女巫的两瓶药
        self.memory.set_variable("has_poison", True)
        self.memory.set_variable("has_antidote", True)
        # 默认策略类型，可以根据需要动态调整
        self.strategy_type = "strategic"  # 可选: "conservative", "aggressive", "strategic", "supportive"

    def set_strategy(self, strategy_type):
        """设置女巫的策略类型"""
        if strategy_type in ["conservative", "aggressive", "strategic", "supportive", "chaos"]:
            self.strategy_type = strategy_type
        else:
            logger.warning(f"未知的策略类型: {strategy_type}, 使用默认策略 'strategic'")
            self.strategy_type = "strategic"

    def perceive(self, req=AgentReq):
        if req.status == STATUS_START:
            self.memory.clear()
            self.memory.set_variable("name", req.name)
            # 重置女巫的两瓶药
            self.memory.set_variable("has_poison", True)
            self.memory.set_variable("has_antidote", True)
            self.memory.append_history("薄伽丘：游戏开始，这是一场6人局狼人杀游戏，包括预言家、女巫、猎人、平民和狼人")
            self.memory.append_history(f"薄伽丘：{req.name}被分配到的角色是[女巫]")
        elif req.status == STATUS_NIGHT:
            # 记录当前夜晚轮次
            current_round = getattr(req, 'round', 1)
            self.memory.set_variable("current_night", current_round)
            self.memory.append_history(f"薄伽丘：第{current_round}夜降临，所有玩家闭眼进入夜晚阶段")
        elif req.status == STATUS_SKILL_RESULT:
            current_night = self.memory.load_variable("current_night") or 1
            self.memory.append_history(f"薄伽丘：第{current_night}夜，女巫{self.memory.load_variable('name')}使用技能，结果：{req.message}")
        elif req.status == STATUS_NIGHT_INFO:
            current_night = self.memory.load_variable("current_night") or 1
            self.memory.append_history(f"薄伽丘：第{current_night}夜结束，天亮了！昨夜的事件：{req.message}")
        elif req.status == STATUS_DISCUSS:  # 发言环节
            if req.name:
                # 其他玩家发言
                self.memory.append_history(f"薄伽丘：{req.name}发言：{req.message}")
            else:
                # 可信发言
                self.memory.append_history(f'薄伽丘：第{req.round}天白天开始，进入发言阶段')
                self.memory.append_history('薄伽丘：每个玩家按顺序描述自己的信息和分析')
        elif req.status == STATUS_VOTE:  # 投票环节
            self.memory.append_history(f"薄伽丘：{req.name}投票：{req.message}")
        elif req.status == STATUS_VOTE_RESULT:  # 投票环节
            out_player = req.name if req.name else req.message
            if out_player:
                self.memory.append_history(f'薄伽丘：投票结果公布，{out_player}被投票出局')
            else:
                self.memory.append_history('薄伽丘：投票结果公布，无人出局')
        elif req.status == STATUS_RESULT:
            self.memory.append_history(f"薄伽丘：游戏结束，{req.message}")
        else:
            raise NotImplementedError

    def interact(self, req=AgentReq) -> AgentResp:
        logger.info("witch interact: {}".format(req))
        if req.status == STATUS_DISCUSS:
            if req.message:
                self.memory.append_history(req.message)
            has_poison = self.memory.load_variable("has_poison")
            has_antidote = self.memory.load_variable("has_antidote")
            skill_info = "女巫有{}瓶毒药和{}瓶解药".format("1" if has_poison else "0", "1" if has_antidote else "0")

            # 使用新的模块化Prompt构建
            prompt = format_prompt(DESC_PROMPT,
                                  {"base_persona": BASE_PERSONA_PROMPT,
                                   "game_rules": GAME_RULES_SUMMARY,
                                   "witch_role": WITCH_ROLE_PROMPT.format(name=self.memory.load_variable("name")),
                                   "action_discuss": ACTION_TEMPLATES["discuss"],
                                   "strategy": STRATEGY_TEMPLATES[self.strategy_type],
                                   "output_discuss": OUTPUT_TEMPLATES["discuss"],
                                   "name": self.memory.load_variable("name"),
                                   "skill_info": skill_info,
                                   "history": "\n".join(self.memory.load_history())
                                  })
            logger.info("prompt:" + prompt)
            result = self.llm_caller(prompt)
            logger.info("witch interact result: {}".format(result))
            return AgentResp(success=True, result=result, errMsg=None)

        elif req.status == STATUS_VOTE:
            self.memory.append_history('薄伽丘：发言结束，现在进入投票阶段，每个人投票选择认为是狼人的玩家')
            choices = [name for name in req.message.split(",") if name != self.memory.load_variable("name")]  # 排除自己
            self.memory.set_variable("choices", choices)
            prompt = format_prompt(VOTE_PROMPT, {"base_persona": BASE_PERSONA_PROMPT,
                                               "game_rules": GAME_RULES_SUMMARY,
                                               "witch_role": WITCH_ROLE_PROMPT.format(name=self.memory.load_variable("name")),
                                               "action_vote": ACTION_TEMPLATES["vote"].format(choices=choices),
                                               "strategy": STRATEGY_TEMPLATES[self.strategy_type],
                                               "output_vote": OUTPUT_TEMPLATES["vote"],
                                               "name": self.memory.load_variable("name"),
                                               "choices": choices,
                                               "history": "\n".join(self.memory.load_history())
                                              })
            logger.info("prompt:" + prompt)
            result = self.llm_caller(prompt)
            logger.info("witch interact result: {}".format(result))
            return AgentResp(success=True, result=result, errMsg=None)

        elif req.status == STATUS_SKILL:
            has_poison = self.memory.load_variable("has_poison")
            has_antidote = self.memory.load_variable("has_antidote")
            tonight_killed = req.message

            choices = [name for name in req.message.split(",") if name != self.memory.load_variable("name")]  # 排除自己
            self.memory.set_variable("choices", choices)

            skill_info = "女巫有{}瓶毒药和{}瓶解药".format("1" if has_poison else "0", "1" if has_antidote else "0")
            prompt = format_prompt(SKILL_PROMPT, {
                "base_persona": BASE_PERSONA_PROMPT,
                "game_rules": GAME_RULES_SUMMARY,
                "witch_role": WITCH_ROLE_PROMPT.format(name=self.memory.load_variable("name")),
                "action_skill": ACTION_TEMPLATES["skill"].format(choices=choices, tonight_killed=tonight_killed),
                "strategy": STRATEGY_TEMPLATES[self.strategy_type],
                "output_skill": OUTPUT_TEMPLATES["skill"].format(tonight_killed=tonight_killed),
                "name": self.memory.load_variable("name"),
                "choices": choices,
                "tonight_killed": tonight_killed,
                "skill_info": skill_info,
                "history": "\n".join(self.memory.load_history())
            })

            logger.info("prompt:" + prompt)
            result = self.llm_caller(prompt)
            logger.info("witch skill result: {}".format(result))
            # 根据结果更新药水状态
            current_night = self.memory.load_variable("current_night") or 1
            skill_target_person = None
            if result.startswith("救") and has_antidote:
                self.memory.set_variable("has_antidote", False)
                self.memory.append_history(f"薄伽丘：第{current_night}夜，女巫{self.memory.load_variable('name')}使用解药救活了{tonight_killed}")
                skill_target_person = tonight_killed
            elif result.startswith("毒") and has_poison:
                poisoned_player = result[1:].strip()
                self.memory.set_variable("has_poison", False)
                self.memory.append_history(f"薄伽丘：第{current_night}夜，女巫{self.memory.load_variable('name')}使用毒药杀死了{poisoned_player}")
                skill_target_person = poisoned_player

            return AgentResp(success=True, result=result, skillTargetPlayer=skill_target_person, errMsg=None)
        else:
            raise NotImplementedError