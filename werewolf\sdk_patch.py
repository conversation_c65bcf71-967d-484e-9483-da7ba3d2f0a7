#!/usr/bin/env python3
"""
SDK 补丁模块
在运行时替换 SDK 的 LLM 调用方法，使其使用我们修复的 Gemini API 实现
"""

import os
import sys

def patch_sdk_llm_caller():
    """补丁 SDK 的 LLM 调用方法"""
    try:
        # 尝试导入 SDK
        from agent_build_sdk.sdk.role_agent import BasicRoleAgent
        from llm_adapter import LLMAdapter
        
        print("正在应用 SDK LLM 调用补丁...")
        
        # 创建我们的 LLM 适配器
        def patched_llm_caller(self, prompt: str) -> str:
            """替换的 LLM 调用方法"""
            try:
                # 使用我们的适配器
                if not hasattr(self, '_llm_adapter'):
                    self._llm_adapter = LLMAdapter(model_name=getattr(self, 'model_name', None))
                
                return self._llm_adapter.llm_caller(prompt)
            except Exception as e:
                print(f"LLM 调用错误: {str(e)}")
                return "抱歉，我现在无法回应。请稍后再试。"
        
        # 替换 BasicRoleAgent 的 llm_caller 方法
        BasicRoleAgent.llm_caller = patched_llm_caller
        
        print("✅ SDK LLM 调用补丁应用成功")
        return True
        
    except ImportError:
        print("⚠️  SDK 不可用，跳过补丁")
        return False
    except Exception as e:
        print(f"❌ 应用补丁失败: {e}")
        return False

def patch_werewolf_agent():
    """补丁 WerewolfAgent 的 LLM 调用"""
    try:
        from agent_build_sdk.sdk.werewolf_agent import WerewolfAgent
        from llm_adapter import LLMAdapter
        
        print("正在应用 WerewolfAgent LLM 调用补丁...")
        
        # 保存原始的初始化方法
        original_init = WerewolfAgent.__init__
        
        def patched_init(self, name, model_name=None):
            """替换的初始化方法"""
            # 调用原始初始化
            original_init(self, name, model_name=model_name)
            
            # 添加我们的适配器
            self._llm_adapter = LLMAdapter(model_name=model_name)
        
        def patched_llm_caller(self, prompt: str) -> str:
            """替换的 LLM 调用方法"""
            try:
                if not hasattr(self, '_llm_adapter'):
                    self._llm_adapter = LLMAdapter(model_name=getattr(self, 'model_name', None))
                
                return self._llm_adapter.llm_caller(prompt)
            except Exception as e:
                print(f"WerewolfAgent LLM 调用错误: {str(e)}")
                return "抱歉，我现在无法回应。请稍后再试。"
        
        # 应用补丁
        WerewolfAgent.__init__ = patched_init
        WerewolfAgent.llm_caller = patched_llm_caller
        
        print("✅ WerewolfAgent LLM 调用补丁应用成功")
        return True
        
    except ImportError:
        print("⚠️  WerewolfAgent 不可用，跳过补丁")
        return False
    except Exception as e:
        print(f"❌ 应用 WerewolfAgent 补丁失败: {e}")
        return False

def apply_all_patches():
    """应用所有补丁"""
    print("=== 应用 SDK 补丁 ===")
    
    # 确保我们的模块在路径中
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    # 加载环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        pass
    
    patches_applied = 0
    
    # 应用 BasicRoleAgent 补丁
    if patch_sdk_llm_caller():
        patches_applied += 1
    
    # 应用 WerewolfAgent 补丁
    if patch_werewolf_agent():
        patches_applied += 1
    
    print(f"\n总共应用了 {patches_applied} 个补丁")
    
    if patches_applied > 0:
        print("🎉 SDK 补丁应用完成！现在应该可以正确使用 Gemini API 了。")
    else:
        print("⚠️  没有应用任何补丁，可能 SDK 不可用。")
    
    return patches_applied > 0

if __name__ == "__main__":
    apply_all_patches()
