#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的LLM客户端模块
支持OpenAI兼容API、Google Gemini和Anthropic Claude
不依赖agent_build_sdk，直接调用各模型API
"""

import os
import json
import asyncio
from typing import Dict, List, Optional, Any, Union
import httpx
from config import game_config


class LLMClient:
    """统一的LLM客户端，支持多种模型系列"""
    
    def __init__(self, model_name: Optional[str] = None, api_key: Optional[str] = None, 
                 base_url: Optional[str] = None, model_series: Optional[str] = None):
        self.model_name = model_name or os.getenv('MODEL_NAME')
        self.api_key = api_key or os.getenv('API_KEY')
        self.base_url = base_url or os.getenv('BASE_URL')
        self.model_series = model_series or game_config.get_model_series()
        
        if not self.model_name:
            raise ValueError("MODEL_NAME must be provided")
        if not self.api_key:
            raise ValueError("API_KEY must be provided")
        if not self.base_url:
            raise ValueError("BASE_URL must be provided")
    
    def chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        统一的聊天完成接口
        
        Args:
            messages: 消息列表，格式为 [{"role": "user", "content": "..."}]
            **kwargs: 其他参数如temperature, max_tokens等
            
        Returns:
            str: 模型回复的文本内容
        """
        if self.model_series == "openai":
            return self._openai_chat_completion(messages, **kwargs)
        elif self.model_series == "gemini":
            return self._gemini_chat_completion(messages, **kwargs)
        elif self.model_series == "claude":
            return self._claude_chat_completion(messages, **kwargs)
        else:
            raise ValueError(f"Unsupported model series: {self.model_series}")
    
    def _openai_chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """OpenAI兼容API调用"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model_name,
            "messages": messages,
            "temperature": kwargs.get("temperature", 0.7),
            "max_tokens": kwargs.get("max_tokens", 1000),
            "stream": False
        }
        
        try:
            with httpx.Client(timeout=60.0) as client:
                response = client.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload
                )
                response.raise_for_status()
                
                result = response.json()
                return result["choices"][0]["message"]["content"]
                
        except httpx.HTTPStatusError as e:
            raise Exception(f"OpenAI API error {e.response.status_code}: {e.response.text}")
        except Exception as e:
            raise Exception(f"OpenAI API call failed: {str(e)}")
    
    def _gemini_chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """Google Gemini API调用"""
        headers = {
            "Content-Type": "application/json"
        }

        # 转换消息格式为Gemini格式
        contents = []
        for msg in messages:
            # Gemini API 只支持 user 和 model 角色
            role = "user" if msg["role"] == "user" else "model"
            contents.append({
                "role": role,
                "parts": [{"text": msg["content"]}]
            })

        payload = {
            "contents": contents,
            "generationConfig": {
                "temperature": kwargs.get("temperature", 0.7),
                "maxOutputTokens": kwargs.get("max_tokens", 1000),
                "candidateCount": 1
            }
        }

        try:
            with httpx.Client(timeout=60.0) as client:
                # Gemini API URL格式：使用API密钥作为查询参数
                url = f"{self.base_url}/v1beta/models/{self.model_name}:generateContent"
                params = {"key": self.api_key}
                response = client.post(url, headers=headers, json=payload, params=params)
                response.raise_for_status()

                result = response.json()
                return result["candidates"][0]["content"]["parts"][0]["text"]

        except httpx.HTTPStatusError as e:
            raise Exception(f"Gemini API error {e.response.status_code}: {e.response.text}")
        except Exception as e:
            raise Exception(f"Gemini API call failed: {str(e)}")
    
    def _claude_chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """Anthropic Claude API调用"""
        headers = {
            "x-api-key": self.api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }
        
        payload = {
            "model": self.model_name,
            "messages": messages,
            "max_tokens": kwargs.get("max_tokens", 1000),
            "temperature": kwargs.get("temperature", 0.7)
        }
        
        try:
            with httpx.Client(timeout=60.0) as client:
                response = client.post(
                    f"{self.base_url}/v1/messages",
                    headers=headers,
                    json=payload
                )
                response.raise_for_status()
                
                result = response.json()
                return result["content"][0]["text"]
                
        except httpx.HTTPStatusError as e:
            raise Exception(f"Claude API error {e.response.status_code}: {e.response.text}")
        except Exception as e:
            raise Exception(f"Claude API call failed: {str(e)}")


class AsyncLLMClient:
    """异步版本的LLM客户端"""
    
    def __init__(self, model_name: Optional[str] = None, api_key: Optional[str] = None, 
                 base_url: Optional[str] = None, model_series: Optional[str] = None):
        self.model_name = model_name or os.getenv('MODEL_NAME')
        self.api_key = api_key or os.getenv('API_KEY')
        self.base_url = base_url or os.getenv('BASE_URL')
        self.model_series = model_series or game_config.get_model_series()
        
        if not self.model_name:
            raise ValueError("MODEL_NAME must be provided")
        if not self.api_key:
            raise ValueError("API_KEY must be provided")
        if not self.base_url:
            raise ValueError("BASE_URL must be provided")
    
    async def chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """异步聊天完成接口"""
        if self.model_series == "openai":
            return await self._openai_chat_completion(messages, **kwargs)
        elif self.model_series == "gemini":
            return await self._gemini_chat_completion(messages, **kwargs)
        elif self.model_series == "claude":
            return await self._claude_chat_completion(messages, **kwargs)
        else:
            raise ValueError(f"Unsupported model series: {self.model_series}")
    
    async def _openai_chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """异步OpenAI兼容API调用"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model_name,
            "messages": messages,
            "temperature": kwargs.get("temperature", 0.7),
            "max_tokens": kwargs.get("max_tokens", 1000),
            "stream": False
        }
        
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload
                )
                response.raise_for_status()
                
                result = response.json()
                return result["choices"][0]["message"]["content"]
                
        except httpx.HTTPStatusError as e:
            raise Exception(f"OpenAI API error {e.response.status_code}: {e.response.text}")
        except Exception as e:
            raise Exception(f"OpenAI API call failed: {str(e)}")
    
    async def _gemini_chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """异步Google Gemini API调用"""
        headers = {
            "Content-Type": "application/json"
        }

        # 转换消息格式为Gemini格式
        contents = []
        for msg in messages:
            # Gemini API 只支持 user 和 model 角色
            role = "user" if msg["role"] == "user" else "model"
            contents.append({
                "role": role,
                "parts": [{"text": msg["content"]}]
            })

        payload = {
            "contents": contents,
            "generationConfig": {
                "temperature": kwargs.get("temperature", 0.7),
                "maxOutputTokens": kwargs.get("max_tokens", 1000),
                "candidateCount": 1
            }
        }

        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                # Gemini API URL格式：使用API密钥作为查询参数
                url = f"{self.base_url}/v1beta/models/{self.model_name}:generateContent"
                params = {"key": self.api_key}
                response = await client.post(url, headers=headers, json=payload, params=params)
                response.raise_for_status()

                result = response.json()
                return result["candidates"][0]["content"]["parts"][0]["text"]

        except httpx.HTTPStatusError as e:
            raise Exception(f"Gemini API error {e.response.status_code}: {e.response.text}")
        except Exception as e:
            raise Exception(f"Gemini API call failed: {str(e)}")
    
    async def _claude_chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """异步Anthropic Claude API调用"""
        headers = {
            "x-api-key": self.api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }
        
        payload = {
            "model": self.model_name,
            "messages": messages,
            "max_tokens": kwargs.get("max_tokens", 1000),
            "temperature": kwargs.get("temperature", 0.7)
        }
        
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    f"{self.base_url}/v1/messages",
                    headers=headers,
                    json=payload
                )
                response.raise_for_status()
                
                result = response.json()
                return result["content"][0]["text"]
                
        except httpx.HTTPStatusError as e:
            raise Exception(f"Claude API error {e.response.status_code}: {e.response.text}")
        except Exception as e:
            raise Exception(f"Claude API call failed: {str(e)}")


def create_llm_client(async_mode: bool = False) -> Union[LLMClient, AsyncLLMClient]:
    """
    创建LLM客户端的工厂函数
    
    Args:
        async_mode: 是否创建异步客户端
        
    Returns:
        LLMClient或AsyncLLMClient实例
    """
    if async_mode:
        return AsyncLLMClient()
    else:
        return LLMClient()


# 兼容性函数，用于替换SDK的llm_caller
def llm_caller(prompt: str, model_name: Optional[str] = None) -> str:
    """
    兼容原有SDK的llm_caller函数
    
    Args:
        prompt: 提示词
        model_name: 模型名称（可选）
        
    Returns:
        str: 模型回复
    """
    client = LLMClient(model_name=model_name)
    messages = [{"role": "user", "content": prompt}]
    return client.chat_completion(messages)
