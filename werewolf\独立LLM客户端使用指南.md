# 独立LLM客户端使用指南

## 概述

本项目现在提供了一个独立的LLM客户端模块，可以直接调用各种大语言模型API，无需依赖agent_build_sdk。这个客户端支持OpenAI兼容API、Google Gemini和Anthropic Claude三大模型系列。

## 特性

### 支持的模型系列

1. **OpenAI兼容API** (`model_series=openai`)
   - OpenAI官方模型：gpt-4, gpt-4-turbo, gpt-3.5-turbo等
   - 阿里云通义千问：qwen-turbo, qwen-plus, qwen-max等
   - 其他兼容OpenAI API格式的模型

2. **Google Gemini** (`model_series=gemini`)
   - gemini-pro, gemini-1.5-pro, gemini-1.5-flash等

3. **Anthropic Claude** (`model_series=claude`)
   - claude-3-opus, claude-3-sonnet, claude-3-haiku, claude-3-5-sonnet等

### 主要功能

- ✅ 统一的API接口，支持多种模型系列
- ✅ 同步和异步两种调用方式
- ✅ 自动处理不同模型的API格式差异
- ✅ 完整的错误处理和重试机制
- ✅ 与现有代码的兼容性适配器
- ✅ 详细的配置验证和调试工具

## 快速开始

### 1. 安装依赖

```bash
pip install httpx>=0.24.0
```

### 2. 配置环境变量

```bash
# 基础配置
export MODEL_SERIES=openai          # 模型系列
export MODEL_NAME=gpt-4            # 模型名称
export API_KEY=your-api-key         # API密钥
export BASE_URL=https://api.openai.com/v1  # API基础URL
```

### 3. 基本使用

```python
from llm_client import LLMClient

# 创建客户端
client = LLMClient()

# 发送消息
messages = [
    {"role": "user", "content": "Hello, how are you?"}
]

response = client.chat_completion(messages)
print(response)
```

## 详细使用说明

### 同步客户端

```python
from llm_client import LLMClient

# 使用环境变量配置
client = LLMClient()

# 或者手动指定配置
client = LLMClient(
    model_name="gpt-4",
    api_key="your-api-key",
    base_url="https://api.openai.com/v1",
    model_series="openai"
)

# 发送消息
messages = [
    {"role": "user", "content": "你好"},
    {"role": "assistant", "content": "你好！有什么可以帮助你的吗？"},
    {"role": "user", "content": "请介绍一下狼人杀游戏"}
]

response = client.chat_completion(
    messages,
    temperature=0.7,
    max_tokens=1000
)
```

### 异步客户端

```python
import asyncio
from llm_client import AsyncLLMClient

async def main():
    client = AsyncLLMClient()
    
    messages = [
        {"role": "user", "content": "Hello!"}
    ]
    
    response = await client.chat_completion(messages)
    print(response)

asyncio.run(main())
```

### 工厂函数

```python
from llm_client import create_llm_client

# 创建同步客户端
sync_client = create_llm_client(async_mode=False)

# 创建异步客户端
async_client = create_llm_client(async_mode=True)
```

### 兼容性适配器

如果你想在现有代码中使用新的LLM客户端，可以使用适配器：

```python
from llm_adapter import llm_caller, BasicRoleAgent

# 直接替换原有的llm_caller函数
response = llm_caller("Hello, world!")

# 替换原有的BasicRoleAgent
agent = BasicRoleAgent(model_name="gpt-4")
response = agent.llm_caller("Hello!")
```

## 配置示例

### OpenAI官方API

```bash
export MODEL_SERIES=openai
export MODEL_NAME=gpt-4
export API_KEY=sk-your-openai-api-key
export BASE_URL=https://api.openai.com/v1
```

### 阿里云通义千问

```bash
export MODEL_SERIES=openai
export MODEL_NAME=qwen-turbo
export API_KEY=your-dashscope-api-key
export BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
```

### Google Gemini

```bash
export MODEL_SERIES=gemini
export MODEL_NAME=gemini-1.5-pro
export API_KEY=your-gemini-api-key
export BASE_URL=https://generativelanguage.googleapis.com
```

### Anthropic Claude

```bash
export MODEL_SERIES=claude
export MODEL_NAME=claude-3-5-sonnet
export API_KEY=your-claude-api-key
export BASE_URL=https://api.anthropic.com
```

## 测试和调试

### 配置验证

```bash
# 检查模型配置
python check_model_config.py

# 测试LLM客户端
python test_llm_client.py

# 调试API连接
python debug_api.py
```

### 测试脚本功能

1. **check_model_config.py**：验证环境变量配置
2. **test_llm_client.py**：测试LLM客户端的各种功能
3. **debug_api.py**：诊断API连接问题

## 错误处理

客户端提供了完整的错误处理机制：

```python
try:
    response = client.chat_completion(messages)
except Exception as e:
    if "404" in str(e):
        print("模型名称可能不正确")
    elif "401" in str(e):
        print("API密钥无效")
    elif "403" in str(e):
        print("权限不足或余额不足")
    else:
        print(f"其他错误: {e}")
```

## 性能优化

### 连接池

客户端使用httpx的连接池来提高性能：

```python
# 客户端会自动管理连接池
# 对于高并发场景，建议复用客户端实例
client = LLMClient()

# 多次调用会复用连接
for i in range(10):
    response = client.chat_completion(messages)
```

### 异步并发

对于需要并发调用的场景，使用异步客户端：

```python
import asyncio
from llm_client import AsyncLLMClient

async def process_multiple_requests():
    client = AsyncLLMClient()
    
    tasks = []
    for i in range(5):
        messages = [{"role": "user", "content": f"Request {i}"}]
        task = client.chat_completion(messages)
        tasks.append(task)
    
    responses = await asyncio.gather(*tasks)
    return responses
```

## 迁移指南

### 从SDK迁移

如果你之前使用agent_build_sdk，可以按以下步骤迁移：

1. **安装新依赖**：
   ```bash
   pip install httpx>=0.24.0
   ```

2. **更新导入**：
   ```python
   # 旧代码
   from agent_build_sdk.sdk.role_agent import BasicRoleAgent
   
   # 新代码
   from llm_adapter import BasicRoleAgent
   ```

3. **配置环境变量**：
   添加`MODEL_SERIES`环境变量

4. **测试功能**：
   运行测试脚本确保一切正常

### 保持兼容性

新的LLM客户端完全兼容原有的接口，你可以：

- 继续使用原有的`llm_caller`函数
- 继续使用原有的`BasicRoleAgent`类
- 逐步迁移到新的API

## 故障排除

### 常见问题

1. **404错误**：检查模型名称是否正确
2. **401错误**：检查API密钥是否有效
3. **403错误**：检查权限和余额
4. **连接超时**：检查网络和BASE_URL

### 调试技巧

1. 使用`debug_api.py`诊断连接问题
2. 检查环境变量是否正确设置
3. 查看详细的错误信息
4. 尝试不同的模型和配置

## 总结

独立LLM客户端提供了：

- 🚀 更好的性能和稳定性
- 🔧 更灵活的配置选项
- 🛠️ 更强的调试和测试工具
- 🔄 完整的向后兼容性
- 📚 详细的文档和示例

通过这个客户端，你可以轻松地在狼人杀游戏中使用各种大语言模型，而无需依赖特定的SDK。
