#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API调试工具
用于诊断API连接和模型配置问题
"""

import os
import sys
from openai import OpenAI

# 尝试加载.env文件
try:
    from dotenv import load_dotenv
    # 尝试从当前目录和上级目录加载.env文件
    if os.path.exists('.env'):
        load_dotenv('.env')
    elif os.path.exists('../.env'):
        load_dotenv('../.env')
    print("已尝试加载.env文件")
except ImportError:
    print("提示: 安装python-dotenv包可以自动加载.env文件")
except Exception as e:
    print(f"加载.env文件时出错: {e}")

def test_api_connection():
    """测试API连接"""
    print("=== API连接测试 ===")
    
    # 获取环境变量
    api_key = os.getenv('API_KEY')
    base_url = os.getenv('BASE_URL')
    model_name = os.getenv('MODEL_NAME')
    model_series = os.getenv('MODEL_SERIES', 'openai')
    
    print(f"模型系列: {model_series}")
    print(f"模型名称: {model_name}")
    print(f"API密钥: {'已设置' if api_key else '未设置'}")
    print(f"基础URL: {base_url}")
    
    if not api_key:
        print("❌ API_KEY 未设置")
        return False
    
    if not base_url:
        print("❌ BASE_URL 未设置")
        return False
        
    if not model_name:
        print("❌ MODEL_NAME 未设置")
        return False
    
    try:
        # 创建OpenAI客户端
        client = OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        
        print("\n正在测试API连接...")
        
        # 发送测试请求
        response = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "user", "content": "Hello, this is a test message."}
            ],
            max_tokens=10
        )
        
        print("✅ API连接成功！")
        print(f"响应内容: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ API连接失败: {str(e)}")
        
        # 提供具体的错误诊断
        error_str = str(e).lower()
        
        if "404" in error_str or "not found" in error_str:
            print("\n🔍 错误诊断:")
            print("- 可能的原因：模型名称不正确")
            print("- 建议检查：")
            print(f"  1. 确认模型名称 '{model_name}' 是否正确")
            print(f"  2. 确认API提供商是否支持该模型")
            print(f"  3. 检查BASE_URL是否正确: {base_url}")
            
        elif "401" in error_str or "unauthorized" in error_str:
            print("\n🔍 错误诊断:")
            print("- 可能的原因：API密钥无效")
            print("- 建议检查：")
            print("  1. API密钥是否正确")
            print("  2. API密钥是否有效（未过期）")
            print("  3. API密钥是否有访问该模型的权限")
            
        elif "403" in error_str or "forbidden" in error_str:
            print("\n🔍 错误诊断:")
            print("- 可能的原因：权限不足")
            print("- 建议检查：")
            print("  1. API密钥是否有访问该模型的权限")
            print("  2. 账户余额是否充足")
            
        elif "timeout" in error_str or "connection" in error_str:
            print("\n🔍 错误诊断:")
            print("- 可能的原因：网络连接问题")
            print("- 建议检查：")
            print("  1. 网络连接是否正常")
            print("  2. BASE_URL是否可访问")
            print("  3. 是否需要代理设置")
            
        return False

def print_common_configurations():
    """打印常见配置示例"""
    print("\n=== 常见配置示例 ===")
    
    configs = [
        {
            "name": "OpenAI官方API",
            "MODEL_SERIES": "openai",
            "MODEL_NAME": "gpt-4",
            "BASE_URL": "https://api.openai.com/v1",
            "note": "需要OpenAI官方API密钥"
        },
        {
            "name": "阿里云通义千问",
            "MODEL_SERIES": "openai", 
            "MODEL_NAME": "qwen-turbo",
            "BASE_URL": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "note": "需要阿里云DashScope API密钥"
        },
        {
            "name": "Google Gemini",
            "MODEL_SERIES": "gemini",
            "MODEL_NAME": "gemini-1.5-pro",
            "BASE_URL": "https://generativelanguage.googleapis.com/v1",
            "note": "需要Google AI API密钥"
        },
        {
            "name": "Anthropic Claude",
            "MODEL_SERIES": "claude",
            "MODEL_NAME": "claude-3-5-sonnet",
            "BASE_URL": "https://api.anthropic.com",
            "note": "需要Anthropic API密钥"
        }
    ]
    
    for config in configs:
        print(f"\n{config['name']}:")
        print(f"  MODEL_SERIES={config['MODEL_SERIES']}")
        print(f"  MODEL_NAME={config['MODEL_NAME']}")
        print(f"  BASE_URL={config['BASE_URL']}")
        print(f"  注意: {config['note']}")

def main():
    """主函数"""
    print("狼人杀游戏API调试工具")
    print("=" * 50)
    
    # 测试API连接
    success = test_api_connection()
    
    if not success:
        print_common_configurations()
        
        print("\n=== 故障排除建议 ===")
        print("1. 检查环境变量设置是否正确")
        print("2. 确认API密钥有效且有足够权限")
        print("3. 验证模型名称是否被API提供商支持")
        print("4. 检查网络连接和BASE_URL可访问性")
        print("5. 查看API提供商的文档确认正确的配置格式")
        
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
