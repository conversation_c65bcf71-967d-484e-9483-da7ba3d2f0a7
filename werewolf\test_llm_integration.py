#!/usr/bin/env python3
"""
测试LLM集成
验证新的LLM客户端系统是否能正确处理Agent调用
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_llm_client_creation():
    """测试LLM客户端创建"""
    try:
        from llm_client import LLMClient
        
        # 测试客户端创建（即使API密钥无效，创建过程应该成功）
        client = LLMClient(
            model_name="test-model",
            api_key="test-key",
            base_url="https://api.openai.com/v1",
            model_series="openai"
        )
        
        print("✅ LLM客户端创建成功")
        print(f"模型名称: {client.model_name}")
        print(f"模型系列: {client.model_series}")
        return True
        
    except Exception as e:
        print(f"❌ LLM客户端创建失败: {e}")
        return False

def test_adapter_llm_call():
    """测试适配器LLM调用（模拟）"""
    try:
        from llm_adapter import LLMAdapter
        
        # 创建适配器
        adapter = LLMAdapter(model_name="test-model")
        
        print("✅ LLM适配器创建成功")
        
        # 注意：这里会失败因为API密钥无效，但我们可以捕获异常
        try:
            result = adapter.llm_caller("测试提示")
            print(f"LLM响应: {result}")
        except Exception as e:
            # 这是预期的，因为API密钥无效
            if "抱歉，我现在无法回应" in str(e) or "LLM调用错误" in str(e):
                print("✅ 适配器错误处理正常（返回默认错误消息）")
            else:
                print(f"⚠️  适配器调用失败（预期的）: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 适配器测试失败: {e}")
        return False

def test_agent_llm_integration():
    """测试Agent与LLM的集成"""
    try:
        from villager.villager_agent import VillagerAgent
        from mock_sdk import AgentReq, STATUS_DISCUSS
        
        # 创建Agent
        agent = VillagerAgent(model_name="test-model")
        print("✅ Agent创建成功")
        
        # 设置一些基本状态
        agent.memory.set_variable("name", "测试玩家")
        agent.memory.append_history("游戏开始")
        
        # 创建一个测试请求
        req = AgentReq(
            status=STATUS_DISCUSS,
            message="这是一个测试消息"
        )
        
        print("✅ 测试请求创建成功")
        
        # 尝试调用interact方法
        try:
            response = agent.interact(req)
            print(f"✅ Agent交互成功")
            print(f"响应成功: {response.success}")
            print(f"响应内容: {response.result[:100]}..." if response.result else "无响应内容")
        except Exception as e:
            # 这可能会失败，因为API调用会失败
            print(f"⚠️  Agent交互失败（预期的，因为API密钥无效）: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent集成测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理机制"""
    try:
        from llm_adapter import LLMAdapter
        
        # 使用无效配置创建适配器
        adapter = LLMAdapter(model_name="invalid-model")
        
        # 尝试调用，应该返回错误消息而不是崩溃
        result = adapter.llm_caller("测试")
        
        if "抱歉，我现在无法回应" in result:
            print("✅ 错误处理机制正常工作")
            return True
        else:
            print(f"⚠️  意外的响应: {result}")
            return True  # 仍然算作成功，因为没有崩溃
            
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("狼人杀游戏 - LLM集成测试")
    print("=" * 50)
    
    # 加载环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ 环境变量加载成功")
    except ImportError:
        print("⚠️  python-dotenv未安装，跳过.env文件加载")
    except Exception as e:
        print(f"⚠️  环境变量加载失败: {e}")
    
    tests = [
        ("LLM客户端创建测试", test_llm_client_creation),
        ("适配器LLM调用测试", test_adapter_llm_call),
        ("Agent LLM集成测试", test_agent_llm_integration),
        ("错误处理测试", test_error_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        else:
            print(f"测试失败: {test_name}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有集成测试通过！")
        print("\n📝 下一步:")
        print("1. 在.env文件中设置有效的API密钥")
        print("2. 运行实际的游戏测试")
        return True
    else:
        print("❌ 部分测试失败，请检查配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
