"""
Mock SDK模块
提供与agent_build_sdk兼容的接口，用于替换SDK依赖
"""

import logging
from typing import Optional, Any
from dataclasses import dataclass

# ===== 角色常量 =====
ROLE_VILLAGER = "villager"
ROLE_WITCH = "witch"
ROLE_SEER = "seer"
ROLE_WOLF = "wolf"
ROLE_HUNTER = "hunter"

# ===== 状态常量 =====
STATUS_START = "start"
STATUS_NIGHT = "night"
STATUS_NIGHT_INFO = "night_info"
STATUS_DAY = "day"
STATUS_DISCUSS = "discuss"
STATUS_VOTE = "vote"
STATUS_VOTE_RESULT = "vote_result"
STATUS_SKILL = "skill"
STATUS_SKILL_RESULT = "skill_result"
STATUS_WOLF_SPEECH = "wolf_speech"
STATUS_RESULT = "result"

# ===== 数据模型 =====
@dataclass
class AgentReq:
    """Agent请求模型"""
    status: str
    name: Optional[str] = None
    message: Optional[str] = None
    round: Optional[int] = None
    
    def __init__(self, status: str = "", name: str = None, message: str = None, round: int = None, **kwargs):
        self.status = status
        self.name = name
        self.message = message
        self.round = round
        # 允许额外的属性
        for key, value in kwargs.items():
            setattr(self, key, value)

@dataclass
class AgentResp:
    """Agent响应模型"""
    success: bool
    result: Optional[str] = None
    skillTargetPlayer: Optional[str] = None
    errMsg: Optional[str] = None
    
    def __init__(self, success: bool = True, result: str = None, skillTargetPlayer: str = None, errMsg: str = None, **kwargs):
        self.success = success
        self.result = result
        self.skillTargetPlayer = skillTargetPlayer
        self.errMsg = errMsg
        # 允许额外的属性
        for key, value in kwargs.items():
            setattr(self, key, value)

# ===== 日志系统 =====
class MockLogger:
    """模拟日志记录器"""
    
    def __init__(self):
        self._logger = logging.getLogger("werewolf_game")
        if not self._logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self._logger.addHandler(handler)
            self._logger.setLevel(logging.INFO)
    
    def info(self, message: str):
        self._logger.info(message)
    
    def warning(self, message: str):
        self._logger.warning(message)
    
    def error(self, message: str):
        self._logger.error(message)
    
    def debug(self, message: str):
        self._logger.debug(message)

# 全局日志实例
logger = MockLogger()

# ===== 模拟模块结构 =====
class MockModel:
    """模拟model模块"""
    
    class roles:
        ROLE_VILLAGER = ROLE_VILLAGER
        ROLE_WITCH = ROLE_WITCH
        ROLE_SEER = ROLE_SEER
        ROLE_WOLF = ROLE_WOLF
        ROLE_HUNTER = ROLE_HUNTER
    
    class werewolf_model:
        AgentReq = AgentReq
        AgentResp = AgentResp
        STATUS_START = STATUS_START
        STATUS_NIGHT = STATUS_NIGHT
        STATUS_NIGHT_INFO = STATUS_NIGHT_INFO
        STATUS_DAY = STATUS_DAY
        STATUS_DISCUSS = STATUS_DISCUSS
        STATUS_VOTE = STATUS_VOTE
        STATUS_VOTE_RESULT = STATUS_VOTE_RESULT
        STATUS_SKILL = STATUS_SKILL
        STATUS_SKILL_RESULT = STATUS_SKILL_RESULT
        STATUS_WOLF_SPEECH = STATUS_WOLF_SPEECH
        STATUS_RESULT = STATUS_RESULT

class MockUtils:
    """模拟utils模块"""
    
    class logger:
        @staticmethod
        def info(message: str):
            logger.info(message)
        
        @staticmethod
        def warning(message: str):
            logger.warning(message)
        
        @staticmethod
        def error(message: str):
            logger.error(message)
        
        @staticmethod
        def debug(message: str):
            logger.debug(message)

class MockSDK:
    """模拟SDK模块"""
    
    class role_agent:
        from llm_adapter import BasicRoleAgentAdapter
        BasicRoleAgent = BasicRoleAgentAdapter

class MockAgentBuildSDK:
    """模拟完整的agent_build_sdk模块"""
    
    model = MockModel()
    utils = MockUtils()
    sdk = MockSDK()

# 创建模块实例
mock_sdk = MockAgentBuildSDK()

# ===== 导出接口 =====
__all__ = [
    'AgentReq', 'AgentResp', 'logger',
    'ROLE_VILLAGER', 'ROLE_WITCH', 'ROLE_SEER', 'ROLE_WOLF', 'ROLE_HUNTER',
    'STATUS_START', 'STATUS_NIGHT', 'STATUS_NIGHT_INFO', 'STATUS_DAY',
    'STATUS_DISCUSS', 'STATUS_VOTE', 'STATUS_VOTE_RESULT', 'STATUS_SKILL',
    'STATUS_SKILL_RESULT', 'STATUS_WOLF_SPEECH', 'STATUS_RESULT',
    'mock_sdk'
]
