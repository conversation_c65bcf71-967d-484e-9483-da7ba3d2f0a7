#!/usr/bin/env python3
"""
测试提示词修复
验证所有角色的提示词都包含了禁止暴露意图的指令
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_prompt_contains_restrictions():
    """测试所有角色的提示词是否包含禁止暴露意图的指令"""
    print("=== 测试提示词修复 ===")
    
    roles = [
        ("狼人", "wolf.prompt"),
        ("平民", "villager.prompt"),
        ("预言家", "seer.prompt"),
        ("女巫", "witch.prompt")
    ]
    
    all_passed = True
    
    for role_name, module_path in roles:
        print(f"\n--- 检查 {role_name} 提示词 ---")
        
        try:
            # 动态导入模块
            if module_path == "wolf.prompt":
                from wolf.prompt import BASE_PERSONA_PROMPT, OUTPUT_TEMPLATES
            elif module_path == "villager.prompt":
                from villager.prompt import BASE_PERSONA_PROMPT, OUTPUT_TEMPLATES
            elif module_path == "seer.prompt":
                from seer.prompt import BASE_PERSONA_PROMPT, OUTPUT_TEMPLATES
            elif module_path == "witch.prompt":
                from witch.prompt import BASE_PERSONA_PROMPT, OUTPUT_TEMPLATES
            
            # 检查基础提示词
            base_checks = [
                ("绝对禁止事项", "🚫 绝对禁止事项" in BASE_PERSONA_PROMPT),
                ("禁止括号解释", "绝对禁止在任何回答中添加括号内的策略解释" in BASE_PERSONA_PROMPT),
                ("禁止暴露想法", "绝对禁止暴露自己的真实想法" in BASE_PERSONA_PROMPT),
                ("禁止旁白注释", "绝对禁止使用任何形式的旁白、注释" in BASE_PERSONA_PROMPT),
                ("只能直接发言", "只能输出角色在游戏中的直接发言" in BASE_PERSONA_PROMPT)
            ]
            
            base_passed = True
            for check_name, check_result in base_checks:
                if check_result:
                    print(f"  ✅ {check_name}: 通过")
                else:
                    print(f"  ❌ {check_name}: 失败")
                    base_passed = False
            
            # 检查输出模板
            output_checks = []
            if "discuss" in OUTPUT_TEMPLATES:
                discuss_template = OUTPUT_TEMPLATES["discuss"]
                output_checks.extend([
                    ("发言模板-禁止括号", "绝对不能在发言后添加括号内的策略解释" in discuss_template),
                    ("发言模板-禁止暴露", "绝对不能暴露自己的" in discuss_template),
                    ("发言模板-禁止旁白", "绝对不能使用任何形式的旁白" in discuss_template)
                ])
            
            output_passed = True
            for check_name, check_result in output_checks:
                if check_result:
                    print(f"  ✅ {check_name}: 通过")
                else:
                    print(f"  ❌ {check_name}: 失败")
                    output_passed = False
            
            role_passed = base_passed and output_passed
            if role_passed:
                print(f"  🎉 {role_name} 提示词修复完成")
            else:
                print(f"  ⚠️  {role_name} 提示词需要进一步修复")
                all_passed = False
                
        except Exception as e:
            print(f"  ❌ {role_name} 测试失败: {e}")
            all_passed = False
    
    return all_passed

def test_prompt_generation():
    """测试提示词生成是否正常工作"""
    print("\n=== 测试提示词生成 ===")
    
    try:
        from wolf.prompt import build_prompt as wolf_build_prompt
        from villager.prompt import build_prompt as villager_build_prompt
        
        # 测试狼人提示词生成
        wolf_prompt = wolf_build_prompt(
            "discuss", 
            "aggressive", 
            name="测试狼人", 
            teammates=["队友1"], 
            history="游戏历史测试"
        )
        
        # 检查生成的提示词是否包含禁止指令
        wolf_checks = [
            "🚫 绝对禁止事项" in wolf_prompt,
            "绝对禁止在任何回答中添加括号内的策略解释" in wolf_prompt,
            "绝对不能在发言后添加括号内的策略解释" in wolf_prompt
        ]
        
        if all(wolf_checks):
            print("✅ 狼人提示词生成正常，包含所有禁止指令")
        else:
            print("❌ 狼人提示词生成有问题")
            return False
        
        # 测试平民提示词生成
        villager_prompt = villager_build_prompt(
            "discuss",
            "rational",
            name="测试平民",
            history="游戏历史测试"
        )
        
        # 检查生成的提示词是否包含禁止指令
        villager_checks = [
            "🚫 绝对禁止事项" in villager_prompt,
            "绝对禁止在任何回答中添加括号内的策略解释" in villager_prompt,
            "绝对不能在发言后添加括号内的策略解释" in villager_prompt
        ]
        
        if all(villager_checks):
            print("✅ 平民提示词生成正常，包含所有禁止指令")
        else:
            print("❌ 平民提示词生成有问题")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 提示词生成测试失败: {e}")
        return False

def show_example_fixed_prompt():
    """展示修复后的提示词示例"""
    print("\n=== 修复后的提示词示例 ===")
    
    try:
        from wolf.prompt import build_prompt
        
        # 生成一个狼人发言的提示词示例
        example_prompt = build_prompt(
            "discuss",
            "aggressive", 
            name="5号",
            teammates=["2号"],
            history="薄伽丘：游戏开始\n薄伽丘：昨晚平安夜\n薄伽丘：6号发言：我是预言家，验了2号是好人"
        )
        
        print("狼人发言提示词示例（已修复）:")
        print("-" * 50)
        print(example_prompt[:500] + "..." if len(example_prompt) > 500 else example_prompt)
        print("-" * 50)
        
        # 检查关键禁止指令
        key_restrictions = [
            "🚫 绝对禁止事项",
            "绝对禁止在任何回答中添加括号内的策略解释",
            "绝对不能在发言后添加括号内的策略解释"
        ]
        
        print("\n关键禁止指令检查:")
        for restriction in key_restrictions:
            if restriction in example_prompt:
                print(f"✅ 包含: {restriction}")
            else:
                print(f"❌ 缺失: {restriction}")
        
        return True
        
    except Exception as e:
        print(f"❌ 示例生成失败: {e}")
        return False

def main():
    """主测试函数"""
    print("狼人杀游戏 - 提示词修复验证")
    print("=" * 50)
    
    tests = [
        ("提示词内容检查", test_prompt_contains_restrictions),
        ("提示词生成测试", test_prompt_generation),
        ("修复示例展示", show_example_fixed_prompt)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        else:
            print(f"测试失败: {test_name}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！提示词修复成功。")
        print("\n📝 修复总结:")
        print("1. ✅ 在所有角色的基础提示词中添加了全局禁止指令")
        print("2. ✅ 在所有输出模板中添加了具体的禁止事项")
        print("3. ✅ 特别强化了狼人角色的反暴露保护")
        print("4. ✅ 确保AI不会在括号中暴露策略意图")
        print("\n现在重新启动游戏，AI应该不会再暴露真实意图了。")
        return True
    else:
        print("❌ 部分测试失败，请检查修复。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
