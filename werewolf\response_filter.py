#!/usr/bin/env python3
"""
响应过滤器
防止AI在回答中暴露真实意图、策略分析或元游戏信息
"""

import re
import json
from typing import List, Dict, Any

class ResponseFilter:
    """响应内容过滤器，移除不当的元游戏信息"""
    
    def __init__(self):
        # 需要过滤的关键词和模式
        self.forbidden_patterns = [
            # 括号内的策略分析
            r'\([^)]*(?:策略|意图|目的|引导|误导|欺骗|伪装|演技|表演)[^)]*\)',
            r'\([^)]*(?:真实身份|实际上|其实|暗示|暴露|泄露)[^)]*\)',
            r'\([^)]*(?:狼队|好人阵营|平民阵营|狼人阵营)[^)]*\)',
            r'\([^)]*(?:带节奏|抢轮次|绑票|冲票)[^)]*\)',
            
            # 中括号内的策略分析
            r'\[[^\]]*(?:策略|意图|目的|引导|误导|欺骗|伪装|演技|表演)[^\]]*\]',
            r'\[[^\]]*(?:真实身份|实际上|其实|暗示|暴露|泄露)[^\]]*\]',
            r'\[[^\]]*(?:狼队|好人阵营|平民阵营|狼人阵营)[^\]]*\]',
            r'\[[^\]]*(?:带节奏|抢轮次|绑票|冲票)[^\]]*\]',
            
            # 花括号内的策略分析
            r'\{[^}]*(?:策略|意图|目的|引导|误导|欺骗|伪装|演技|表演)[^}]*\}',
            r'\{[^}]*(?:真实身份|实际上|其实|暗示|暴露|泄露)[^}]*\}',
            r'\{[^}]*(?:狼队|好人阵营|平民阵营|狼人阵营)[^}]*\}',
            r'\{[^}]*(?:带节奏|抢轮次|绑票|冲票)[^}]*\}',
            
            # 直接的策略暴露
            r'(?:我的真实意图是|我的策略是|我想要|我试图|我打算).*?(?:误导|欺骗|引导|伪装)',
            r'(?:作为|身为).*?(?:狼人|平民|预言家|女巫).*?(?:我需要|我应该|我要).*?(?:伪装|欺骗|误导)',
            r'(?:为了|目的是).*?(?:混淆|误导|欺骗|伪装|隐藏身份)',
            
            # 元游戏分析
            r'(?:这样说是为了|这么做是想|这样可以).*?(?:误导|欺骗|混淆)',
            r'(?:通过|利用).*?(?:遗言|发言|投票).*?(?:坐实|证明|误导|欺骗)',
            r'(?:为.*?创造|给.*?制造).*?(?:生存空间|机会|条件)',
            
            # 阵营分析泄露
            r'(?:狼队|好人|平民阵营|狼人阵营).*?(?:已经|现在|目前).*?(?:绑票|领先|落后)',
            r'(?:将矛头指向|把怀疑转移到|让大家怀疑).*?(?:真|假).*?(?:预言家|女巫|平民)',
        ]
        
        # 编译正则表达式以提高性能
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.forbidden_patterns]
        
        # 需要完全移除的短语
        self.forbidden_phrases = [
            "通过遗言坐实预言家身份",
            "为队友创造生存空间",
            "试图引导女巫错误毒杀",
            "将矛头指向真预言家",
            "误导好人",
            "欺骗大家",
            "伪装身份",
            "隐藏真实意图",
            "狼队配合",
            "好人阵营",
            "狼人阵营",
            "平民阵营",
            "带节奏",
            "抢轮次",
            "绑票",
            "冲票"
        ]
    
    def filter_response(self, response: str) -> str:
        """过滤响应内容，移除不当的元游戏信息"""
        if not response:
            return response
        
        filtered_response = response
        
        # 移除匹配的模式
        for pattern in self.compiled_patterns:
            filtered_response = pattern.sub('', filtered_response)
        
        # 移除禁用短语
        for phrase in self.forbidden_phrases:
            filtered_response = filtered_response.replace(phrase, '')
        
        # 清理多余的空格和标点
        filtered_response = re.sub(r'\s+', ' ', filtered_response)  # 多个空格变成一个
        filtered_response = re.sub(r'\s*[,，]\s*[,，]+', '，', filtered_response)  # 多个逗号
        filtered_response = re.sub(r'\s*[.。]\s*[.。]+', '。', filtered_response)  # 多个句号
        filtered_response = re.sub(r'^\s+|\s+$', '', filtered_response)  # 首尾空格
        
        # 如果过滤后内容为空或太短，返回一个通用回复
        if len(filtered_response.strip()) < 10:
            return "我需要仔细考虑当前的局势。"
        
        return filtered_response
    
    def validate_response(self, response: str) -> Dict[str, Any]:
        """验证响应是否包含不当内容"""
        issues = []
        
        # 检查是否包含策略泄露
        for i, pattern in enumerate(self.compiled_patterns):
            matches = pattern.findall(response)
            if matches:
                issues.append({
                    "type": "pattern_match",
                    "pattern_index": i,
                    "matches": matches
                })
        
        # 检查是否包含禁用短语
        for phrase in self.forbidden_phrases:
            if phrase in response:
                issues.append({
                    "type": "forbidden_phrase",
                    "phrase": phrase
                })
        
        return {
            "is_valid": len(issues) == 0,
            "issues": issues,
            "filtered_response": self.filter_response(response)
        }

# 全局过滤器实例
response_filter = ResponseFilter()

def filter_llm_response(response: str) -> str:
    """过滤LLM响应的便捷函数"""
    return response_filter.filter_response(response)

def validate_llm_response(response: str) -> Dict[str, Any]:
    """验证LLM响应的便捷函数"""
    return response_filter.validate_response(response)

# 测试函数
def test_filter():
    """测试过滤器功能"""
    test_cases = [
        "作为被投票出局的狼人，我的遗言是：6号绝对是悍跳狼！(通过遗言坐实预言家身份，为队友5号创造生存空间)",
        "我觉得2号很可疑，大家应该投票给他。",
        "昨晚是平安夜，说明女巫救了人。[这样说是为了误导大家]",
        "我是预言家，验了3号是好人。{实际上我是狼人，想要伪装身份}",
    ]
    
    print("=== 响应过滤器测试 ===")
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试案例 {i}:")
        print(f"原始: {test_case}")
        
        validation = validate_llm_response(test_case)
        print(f"验证结果: {'通过' if validation['is_valid'] else '失败'}")
        
        if not validation['is_valid']:
            print(f"问题数量: {len(validation['issues'])}")
        
        filtered = filter_llm_response(test_case)
        print(f"过滤后: {filtered}")

if __name__ == "__main__":
    test_filter()
