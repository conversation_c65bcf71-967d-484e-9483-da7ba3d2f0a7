import os

# 使用我们的适配器系统而不是原始SDK
try:
    from agent_build_sdk.builder import Agent<PERSON><PERSON>er
    from agent_build_sdk.sdk.werewolf_agent import WerewolfAgent
    from agent_build_sdk.model.roles import ROLE_VILLAGER,ROLE_WOLF,ROLE_SEER,ROLE_WITCH,ROLE_HUNTER
    SDK_AVAILABLE = True
except ImportError:
    print("⚠️  原始SDK不可用，将使用适配器系统")
    SDK_AVAILABLE = False

from seer.seer_agent import SeerAgent
from villager.villager_agent import VillagerAgent
from witch.witch_agent import WitchAgent
from wolf.wolf_agent import WolfAgent
from config import game_config

# 如果SDK不可用，使用我们的mock版本
if not SDK_AVAILABLE:
    from mock_sdk import ROLE_VILLAGER, ROLE_WOLF, ROLE_SEER, ROLE_WITCH, ROLE_HUNTER

if __name__ == '__main__':
    # 在启动前应用 SDK 补丁，修复 Gemini API 调用问题
    try:
        from sdk_patch import apply_all_patches
        apply_all_patches()
    except Exception as e:
        print(f"⚠️  应用 SDK 补丁时出错: {e}")

    name = 'spy'
    model_name = os.getenv('MODEL_NAME')

    # 输出模型配置信息
    print("=== 模型配置信息 ===")
    print(f"模型系列: {game_config.get_model_series()}")
    print(f"模型名称: {model_name}")
    print(f"系列说明: {game_config.get_supported_models()}")

    # 验证模型配置
    if model_name:
        if game_config.validate_model_config(model_name):
            print("✓ 模型配置验证通过")
        else:
            print("⚠ 模型配置验证失败，但将继续运行")
    else:
        print("⚠ 未设置MODEL_NAME环境变量")

    agent = WerewolfAgent(name, model_name=model_name)

    # 创建角色Agent实例
    villager_agent = VillagerAgent(model_name=model_name)
    wolf_agent = WolfAgent(model_name=model_name)
    seer_agent = SeerAgent(model_name=model_name)
    witch_agent = WitchAgent(model_name=model_name)

    # 根据配置设置策略
    villager_agent.set_strategy(game_config.get_strategy("villager"))
    wolf_agent.set_strategy(game_config.get_strategy("wolf"))
    seer_agent.set_strategy(game_config.get_strategy("seer"))
    witch_agent.set_strategy(game_config.get_strategy("witch"))

    # 注册角色Agent
    agent.register_role_agent(ROLE_VILLAGER, villager_agent)
    agent.register_role_agent(ROLE_WOLF, wolf_agent)
    agent.register_role_agent(ROLE_SEER, seer_agent)
    agent.register_role_agent(ROLE_WITCH, witch_agent)

    # 输出当前配置状态
    print("\n=== 游戏策略配置 ===")
    if game_config.is_chaos_mode():
        print("混乱模式: 已启用")
        print("平民策略:", game_config.get_strategy("villager"))
        print("女巫策略:", game_config.get_strategy("witch"))
        print("预言家策略:", game_config.get_strategy("seer"))
        print("狼人策略:", game_config.get_strategy("wolf"))
    else:
        print("混乱模式: 关闭（常规模式）")
        print("平民策略:", game_config.get_strategy("villager"))
        print("女巫策略:", game_config.get_strategy("witch"))
        print("预言家策略:", game_config.get_strategy("seer"))
        print("狼人策略:", game_config.get_strategy("wolf"))

    agent_builder = AgentBuilder(name, agent=agent)
    agent_builder.start()