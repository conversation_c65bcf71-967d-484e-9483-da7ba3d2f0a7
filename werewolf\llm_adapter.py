#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM适配器模块
用于在现有的agent代码中无缝使用新的LLM客户端
替换原有的SDK依赖
"""

import os
from typing import Optional
from llm_client import LLMClient


class LLMAdapter:
    """
    LLM适配器类
    提供与原有SDK兼容的接口，内部使用新的LLM客户端
    """
    
    def __init__(self, model_name: Optional[str] = None):
        """
        初始化适配器
        
        Args:
            model_name: 模型名称，如果不提供则从环境变量获取
        """
        self.model_name = model_name or os.getenv('MODEL_NAME')
        self._client = None
    
    @property
    def client(self) -> LLMClient:
        """懒加载LLM客户端"""
        if self._client is None:
            self._client = LLMClient(model_name=self.model_name)
        return self._client
    
    def llm_caller(self, prompt: str) -> str:
        """
        兼容原有SDK的llm_caller方法
        
        Args:
            prompt: 提示词
            
        Returns:
            str: 模型回复
        """
        try:
            messages = [{"role": "user", "content": prompt}]
            response = self.client.chat_completion(messages)
            return response
        except Exception as e:
            # 记录错误但不中断游戏
            print(f"LLM调用错误: {str(e)}")
            return "抱歉，我现在无法回应。请稍后再试。"


# 全局适配器实例
_global_adapter = None


def get_llm_adapter(model_name: Optional[str] = None) -> LLMAdapter:
    """
    获取全局LLM适配器实例
    
    Args:
        model_name: 模型名称
        
    Returns:
        LLMAdapter: 适配器实例
    """
    global _global_adapter
    if _global_adapter is None or (model_name and model_name != _global_adapter.model_name):
        _global_adapter = LLMAdapter(model_name)
    return _global_adapter


def llm_caller(prompt: str, model_name: Optional[str] = None) -> str:
    """
    全局LLM调用函数，兼容原有SDK接口
    
    Args:
        prompt: 提示词
        model_name: 模型名称（可选）
        
    Returns:
        str: 模型回复
    """
    adapter = get_llm_adapter(model_name)
    return adapter.llm_caller(prompt)


class SimpleMemory:
    """简单的内存管理类，模拟SDK的memory功能"""

    def __init__(self):
        self._variables = {}
        self._history = []

    def clear(self):
        """清空内存"""
        self._variables.clear()
        self._history.clear()

    def set_variable(self, key: str, value):
        """设置变量"""
        self._variables[key] = value

    def load_variable(self, key: str):
        """加载变量"""
        return self._variables.get(key)

    def append_history(self, message: str):
        """添加历史记录"""
        self._history.append(message)

    def load_history(self):
        """加载历史记录"""
        return self._history.copy()


class BasicRoleAgentAdapter:
    """
    基础角色Agent适配器
    用于替换原有的BasicRoleAgent类
    """

    def __init__(self, role: Optional[str] = None, model_name: Optional[str] = None):
        """
        初始化Agent适配器

        Args:
            role: 角色类型（兼容原SDK接口）
            model_name: 模型名称
        """
        self.role = role
        self.model_name = model_name
        self.adapter = LLMAdapter(model_name)
        self.memory = SimpleMemory()

    def llm_caller(self, prompt: str) -> str:
        """
        LLM调用方法

        Args:
            prompt: 提示词

        Returns:
            str: 模型回复
        """
        return self.adapter.llm_caller(prompt)


# 用于替换SDK导入的兼容性函数
def create_basic_role_agent(role: Optional[str] = None, model_name: Optional[str] = None) -> BasicRoleAgentAdapter:
    """
    创建基础角色Agent的工厂函数

    Args:
        role: 角色类型
        model_name: 模型名称

    Returns:
        BasicRoleAgentAdapter: Agent适配器实例
    """
    return BasicRoleAgentAdapter(role, model_name)


# 模拟SDK的类和函数，用于兼容性
class MockSDK:
    """模拟SDK类，提供兼容性接口"""

    @staticmethod
    def BasicRoleAgent(role: Optional[str] = None, model_name: Optional[str] = None):
        """模拟BasicRoleAgent类"""
        return BasicRoleAgentAdapter(role, model_name)


# 导出兼容性接口
BasicRoleAgent = BasicRoleAgentAdapter
