#!/usr/bin/env python3
"""
修复 Gemini API 404 错误
专门针对 Gemini API 调用的 404 错误进行诊断和修复
"""

import os
import sys
import httpx
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gemini_api_variations():
    """测试不同的 Gemini API 调用方式"""
    print("=== 测试 Gemini API 调用变体 ===")
    
    # 从环境变量或使用测试值
    model_name = os.getenv('MODEL_NAME', 'gemini-1.5-pro')
    api_key = os.getenv('API_KEY')
    base_url = os.getenv('BASE_URL', 'https://generativelanguage.googleapis.com')
    
    if not api_key:
        print("❌ 需要设置 API_KEY 环境变量")
        return False
    
    print(f"模型名称: {model_name}")
    print(f"基础URL: {base_url}")
    
    # 测试不同的 API 端点变体
    api_variants = [
        # 标准 v1beta 端点
        {
            "name": "v1beta 端点",
            "url": f"{base_url}/v1beta/models/{model_name}:generateContent",
            "auth_method": "query_param"
        },
        # v1 端点
        {
            "name": "v1 端点", 
            "url": f"{base_url}/v1/models/{model_name}:generateContent",
            "auth_method": "query_param"
        },
        # 不带版本的端点
        {
            "name": "无版本端点",
            "url": f"{base_url}/models/{model_name}:generateContent", 
            "auth_method": "query_param"
        },
        # 使用 Authorization header
        {
            "name": "v1beta + Authorization header",
            "url": f"{base_url}/v1beta/models/{model_name}:generateContent",
            "auth_method": "header"
        }
    ]
    
    # 测试请求负载
    test_payload = {
        "contents": [
            {
                "role": "user",
                "parts": [{"text": "Hello, please respond briefly."}]
            }
        ],
        "generationConfig": {
            "temperature": 0.7,
            "maxOutputTokens": 50,
            "candidateCount": 1
        }
    }
    
    headers_base = {"Content-Type": "application/json"}
    
    for variant in api_variants:
        print(f"\n--- 测试 {variant['name']} ---")
        print(f"URL: {variant['url']}")
        
        try:
            headers = headers_base.copy()
            params = {}
            
            if variant['auth_method'] == 'query_param':
                params['key'] = api_key
            else:
                headers['Authorization'] = f'Bearer {api_key}'
            
            with httpx.Client(timeout=30.0) as client:
                response = client.post(
                    variant['url'], 
                    headers=headers, 
                    json=test_payload,
                    params=params if params else None
                )
                
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ 成功！")
                    result = response.json()
                    if "candidates" in result and result["candidates"]:
                        content = result["candidates"][0]["content"]["parts"][0]["text"]
                        print(f"响应: {content}")
                        return variant  # 返回成功的变体
                    else:
                        print("⚠️  响应格式异常")
                        print(f"响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
                else:
                    print(f"❌ 失败")
                    print(f"响应: {response.text}")
                    
        except Exception as e:
            print(f"❌ 请求异常: {e}")
    
    return None

def update_llm_client_with_fix(working_variant):
    """根据工作的变体更新 LLM 客户端"""
    if not working_variant:
        print("❌ 没有找到工作的 API 变体")
        return False
    
    print(f"\n=== 更新 LLM 客户端配置 ===")
    print(f"使用工作的变体: {working_variant['name']}")
    
    # 读取当前的 llm_client.py
    try:
        with open('llm_client.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 根据工作的变体生成新的实现
        if 'v1beta' in working_variant['url']:
            api_version = 'v1beta'
        elif 'v1' in working_variant['url']:
            api_version = 'v1'
        else:
            api_version = ''
        
        auth_method = working_variant['auth_method']
        
        # 生成新的 Gemini API 调用代码
        new_gemini_code = f'''    def _gemini_chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """Google Gemini API调用 - 修复版"""
        headers = {{
            "Content-Type": "application/json"
        }}
        
        # 转换消息格式为Gemini格式
        contents = []
        for msg in messages:
            role = "user" if msg["role"] == "user" else "model"
            contents.append({{
                "role": role,
                "parts": [{{"text": msg["content"]}}]
            }})
        
        payload = {{
            "contents": contents,
            "generationConfig": {{
                "temperature": kwargs.get("temperature", 0.7),
                "maxOutputTokens": kwargs.get("max_tokens", 1000),
                "candidateCount": 1
            }}
        }}
        
        try:
            with httpx.Client(timeout=60.0) as client:'''
        
        if api_version:
            new_gemini_code += f'''
                url = f"{{self.base_url}}/{api_version}/models/{{self.model_name}}:generateContent"'''
        else:
            new_gemini_code += f'''
                url = f"{{self.base_url}}/models/{{self.model_name}}:generateContent"'''
        
        if auth_method == 'query_param':
            new_gemini_code += '''
                params = {"key": self.api_key}
                response = client.post(url, headers=headers, json=payload, params=params)'''
        else:
            new_gemini_code += '''
                headers["Authorization"] = f"Bearer {self.api_key}"
                response = client.post(url, headers=headers, json=payload)'''
        
        new_gemini_code += '''
                response.raise_for_status()
                
                result = response.json()
                return result["candidates"][0]["content"]["parts"][0]["text"]
                
        except httpx.HTTPStatusError as e:
            raise Exception(f"Gemini API error {e.response.status_code}: {e.response.text}")
        except Exception as e:
            raise Exception(f"Gemini API call failed: {str(e)}")'''
        
        print("✅ 生成了修复的 Gemini API 代码")
        print("请手动更新 llm_client.py 文件中的 _gemini_chat_completion 方法")
        print("\n修复代码:")
        print(new_gemini_code)
        
        return True
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False

def main():
    """主函数"""
    print("Gemini API 404 错误修复工具")
    print("=" * 50)
    
    # 加载环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ 环境变量加载成功")
    except ImportError:
        print("⚠️  python-dotenv 未安装")
    except Exception as e:
        print(f"⚠️  环境变量加载失败: {e}")
    
    # 检查当前配置
    model_series = os.getenv('MODEL_SERIES')
    if model_series != 'gemini':
        print(f"⚠️  当前模型系列是 '{model_series}'，不是 'gemini'")
        print("如果要修复 Gemini API，请设置 MODEL_SERIES=gemini")
        return 1
    
    # 测试不同的 API 变体
    working_variant = test_gemini_api_variations()
    
    if working_variant:
        print(f"\n🎉 找到工作的 API 变体: {working_variant['name']}")
        update_llm_client_with_fix(working_variant)
        return 0
    else:
        print("\n❌ 没有找到工作的 API 变体")
        print("\n可能的解决方案:")
        print("1. 检查 API 密钥是否正确")
        print("2. 确认模型名称是否存在")
        print("3. 检查网络连接")
        print("4. 确认 API 配额是否充足")
        return 1

if __name__ == "__main__":
    sys.exit(main())
