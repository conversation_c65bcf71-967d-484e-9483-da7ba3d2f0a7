#!/usr/bin/env python3
"""
测试 Gemini API 连接
验证修复后的 Gemini API 调用是否正常工作
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gemini_api_direct():
    """直接测试 Gemini API 调用"""
    try:
        from llm_client import LLMClient
        
        # 从环境变量获取配置
        model_name = os.getenv('MODEL_NAME')
        api_key = os.getenv('API_KEY')
        base_url = os.getenv('BASE_URL')
        model_series = os.getenv('MODEL_SERIES')
        
        print(f"模型系列: {model_series}")
        print(f"模型名称: {model_name}")
        print(f"API密钥: {'已设置' if api_key else '未设置'}")
        print(f"基础URL: {base_url}")
        
        if not all([model_name, api_key, base_url]):
            print("❌ 环境变量配置不完整")
            return False
        
        if model_series != "gemini":
            print("⚠️  当前配置不是 Gemini 系列，跳过测试")
            return True
        
        # 创建 LLM 客户端
        client = LLMClient(
            model_name=model_name,
            api_key=api_key,
            base_url=base_url,
            model_series=model_series
        )
        
        print("\n正在测试 Gemini API 调用...")
        
        # 测试消息
        messages = [{"role": "user", "content": "你好，请简单回复一下。"}]
        
        # 调用 API
        response = client.chat_completion(messages)
        
        print("✅ Gemini API 调用成功！")
        print(f"响应内容: {response}")
        return True
        
    except Exception as e:
        print(f"❌ Gemini API 调用失败: {e}")
        
        # 提供详细的错误诊断
        error_str = str(e).lower()
        
        if "404" in error_str:
            print("\n🔍 错误诊断:")
            print("- 可能的原因：API 端点不正确或模型名称错误")
            print("- 建议检查：")
            print(f"  1. 模型名称是否正确: {model_name}")
            print(f"  2. BASE_URL 是否正确: {base_url}")
            print("  3. 确认使用的是 v1beta 端点")
            
        elif "401" in error_str or "403" in error_str:
            print("\n🔍 错误诊断:")
            print("- 可能的原因：API 密钥无效或权限不足")
            print("- 建议检查：")
            print("  1. API 密钥是否正确")
            print("  2. API 密钥是否有访问该模型的权限")
            print("  3. 确认 API 密钥格式正确")
            
        elif "400" in error_str:
            print("\n🔍 错误诊断:")
            print("- 可能的原因：请求格式错误")
            print("- 建议检查：")
            print("  1. 请求参数格式是否正确")
            print("  2. 模型名称格式是否符合要求")
            
        return False

def test_adapter_with_gemini():
    """测试适配器与 Gemini 的集成"""
    try:
        from llm_adapter import LLMAdapter
        
        model_series = os.getenv('MODEL_SERIES')
        if model_series != "gemini":
            print("⚠️  当前配置不是 Gemini 系列，跳过适配器测试")
            return True
        
        print("\n--- 测试 LLM 适配器与 Gemini 集成 ---")
        
        # 创建适配器
        adapter = LLMAdapter()
        
        # 测试调用
        response = adapter.llm_caller("请简单介绍一下狼人杀游戏。")
        
        print("✅ LLM 适配器与 Gemini 集成成功！")
        print(f"响应内容: {response[:100]}..." if len(response) > 100 else response)
        return True
        
    except Exception as e:
        print(f"❌ 适配器测试失败: {e}")
        return False

def test_agent_with_gemini():
    """测试 Agent 与 Gemini 的集成"""
    try:
        from villager.villager_agent import VillagerAgent
        from mock_sdk import AgentReq, STATUS_DISCUSS
        
        model_series = os.getenv('MODEL_SERIES')
        if model_series != "gemini":
            print("⚠️  当前配置不是 Gemini 系列，跳过 Agent 测试")
            return True
        
        print("\n--- 测试 Agent 与 Gemini 集成 ---")
        
        # 创建 Agent
        agent = VillagerAgent(model_name=os.getenv('MODEL_NAME'))
        
        # 设置基本状态
        agent.memory.set_variable("name", "测试玩家")
        agent.memory.append_history("游戏开始")
        
        # 创建测试请求
        req = AgentReq(
            status=STATUS_DISCUSS,
            message="这是一个测试消息"
        )
        
        # 测试交互
        response = agent.interact(req)
        
        print("✅ Agent 与 Gemini 集成成功！")
        print(f"响应成功: {response.success}")
        print(f"响应内容: {response.result[:100]}..." if response.result and len(response.result) > 100 else response.result)
        return True
        
    except Exception as e:
        print(f"❌ Agent 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Gemini API 连接测试")
    print("=" * 50)
    
    # 加载环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ 环境变量加载成功")
    except ImportError:
        print("⚠️  python-dotenv 未安装，跳过 .env 文件加载")
    except Exception as e:
        print(f"⚠️  环境变量加载失败: {e}")
    
    tests = [
        ("Gemini API 直接调用测试", test_gemini_api_direct),
        ("LLM 适配器集成测试", test_adapter_with_gemini),
        ("Agent 集成测试", test_agent_with_gemini),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        else:
            print(f"测试失败: {test_name}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有 Gemini 测试通过！")
        return True
    else:
        print("❌ 部分测试失败，请检查配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
