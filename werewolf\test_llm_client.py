#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM客户端测试脚本
测试各个模型系列的调用是否正常
"""

import os
import sys
import asyncio
from llm_client import LLMClient, AsyncLLMClient, create_llm_client


def test_sync_client():
    """测试同步客户端"""
    print("=== 同步客户端测试 ===")
    
    try:
        client = create_llm_client(async_mode=False)
        
        # 测试消息
        messages = [
            {"role": "user", "content": "Hello! Please respond with 'Test successful' if you can understand this message."}
        ]
        
        print(f"使用模型: {client.model_name}")
        print(f"模型系列: {client.model_series}")
        print(f"API地址: {client.base_url}")
        
        response = client.chat_completion(messages, max_tokens=50, temperature=0.1)
        print(f"模型回复: {response}")
        
        return True
        
    except Exception as e:
        print(f"同步客户端测试失败: {str(e)}")
        return False


async def test_async_client():
    """测试异步客户端"""
    print("\n=== 异步客户端测试 ===")
    
    try:
        client = create_llm_client(async_mode=True)
        
        # 测试消息
        messages = [
            {"role": "user", "content": "Hello! Please respond with 'Async test successful' if you can understand this message."}
        ]
        
        print(f"使用模型: {client.model_name}")
        print(f"模型系列: {client.model_series}")
        print(f"API地址: {client.base_url}")
        
        response = await client.chat_completion(messages, max_tokens=50, temperature=0.1)
        print(f"模型回复: {response}")
        
        return True
        
    except Exception as e:
        print(f"异步客户端测试失败: {str(e)}")
        return False


def test_different_model_series():
    """测试不同模型系列的配置"""
    print("\n=== 不同模型系列配置测试 ===")
    
    # 测试配置示例
    test_configs = [
        {
            "name": "OpenAI兼容API",
            "model_series": "openai",
            "model_name": "gpt-4",
            "base_url": "https://api.openai.com/v1",
            "note": "需要OpenAI API密钥"
        },
        {
            "name": "Google Gemini",
            "model_series": "gemini",
            "model_name": "gemini-1.5-pro",
            "base_url": "https://generativelanguage.googleapis.com",
            "note": "需要Google AI API密钥"
        },
        {
            "name": "Anthropic Claude",
            "model_series": "claude",
            "model_name": "claude-3-5-sonnet",
            "base_url": "https://api.anthropic.com",
            "note": "需要Anthropic API密钥"
        }
    ]
    
    for config in test_configs:
        print(f"\n{config['name']}:")
        print(f"  模型系列: {config['model_series']}")
        print(f"  模型名称: {config['model_name']}")
        print(f"  API地址: {config['base_url']}")
        print(f"  注意: {config['note']}")
        
        # 检查是否可以创建客户端（不实际调用API）
        try:
            # 使用测试API密钥创建客户端
            client = LLMClient(
                model_name=config['model_name'],
                api_key="test_key",
                base_url=config['base_url'],
                model_series=config['model_series']
            )
            print(f"  ✓ 客户端创建成功")
        except Exception as e:
            print(f"  ✗ 客户端创建失败: {str(e)}")


def test_environment_variables():
    """测试环境变量配置"""
    print("\n=== 环境变量配置测试 ===")
    
    required_vars = ['MODEL_SERIES', 'MODEL_NAME', 'API_KEY', 'BASE_URL']
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            # 对于API密钥，只显示前几位和后几位
            if 'KEY' in var and len(value) > 10:
                display_value = f"{value[:4]}...{value[-4:]}"
            else:
                display_value = value
            print(f"  ✓ {var}: {display_value}")
        else:
            print(f"  ✗ {var}: 未设置")


def test_compatibility_function():
    """测试兼容性函数"""
    print("\n=== 兼容性函数测试 ===")
    
    try:
        from llm_client import llm_caller
        
        # 测试兼容性函数
        prompt = "Please respond with 'Compatibility test successful'."
        response = llm_caller(prompt)
        print(f"兼容性函数调用成功: {response}")
        
        return True
        
    except Exception as e:
        print(f"兼容性函数测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("LLM客户端测试工具")
    print("=" * 50)
    
    # 检查环境变量
    test_environment_variables()
    
    # 测试不同模型系列配置
    test_different_model_series()
    
    # 检查是否有完整的环境变量配置
    required_vars = ['MODEL_NAME', 'API_KEY', 'BASE_URL']
    has_config = all(os.getenv(var) for var in required_vars)
    
    if has_config:
        print("\n检测到完整的环境变量配置，开始API调用测试...")
        
        # 测试同步客户端
        sync_success = test_sync_client()
        
        # 测试异步客户端
        async_success = await test_async_client()
        
        # 测试兼容性函数
        compat_success = test_compatibility_function()
        
        # 总结
        print("\n=== 测试结果总结 ===")
        print(f"同步客户端: {'✓ 成功' if sync_success else '✗ 失败'}")
        print(f"异步客户端: {'✓ 成功' if async_success else '✗ 失败'}")
        print(f"兼容性函数: {'✓ 成功' if compat_success else '✗ 失败'}")
        
        if all([sync_success, async_success, compat_success]):
            print("\n🎉 所有测试通过！LLM客户端工作正常。")
            return 0
        else:
            print("\n⚠️ 部分测试失败，请检查配置和网络连接。")
            return 1
    else:
        print("\n⚠️ 环境变量配置不完整，跳过API调用测试。")
        print("请设置以下环境变量后重新运行测试：")
        for var in required_vars:
            if not os.getenv(var):
                print(f"  - {var}")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")
        sys.exit(1)
